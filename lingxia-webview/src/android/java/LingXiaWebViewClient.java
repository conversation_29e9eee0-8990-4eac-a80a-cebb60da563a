package com.lingxia.webview;

import android.util.Log;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import org.json.JSONObject;
import java.io.ByteArrayInputStream;
import java.lang.ref.WeakReference;

/**
 * WebViewClient implementation for LingXia WebView
 */
public class LingXiaWebViewClient extends WebViewClient {
    private static final String TAG = "LingXiaWebViewClient";
    private final WeakReference<LingXiaWebView> webViewRef;

    public LingXiaWebViewClient(LingXiaWebView webView) {
        this.webViewRef = new WeakReference<>(webView);
    }

    @Override
    public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
        super.onPageStarted(view, url, favicon);
        Log.d(TAG, "Page started loading: " + url);

        // Clear session for feishu OAuth to force authorization page
        if (url.contains("feishu.cn") && url.contains("authen")) {
            Log.d(TAG, "Clearing feishu session to force authorization");
            android.webkit.CookieManager cookieManager = android.webkit.CookieManager.getInstance();
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                cookieManager.removeAllCookies(null);
                cookieManager.flush();
            } else {
                cookieManager.removeAllCookie();
            }
        }

        LingXiaWebView webView = webViewRef.get();
        if (webView != null) {
            webView.setPageLoaded(false);
            webView.onPageStarted(
                webView.getAppId() != null ? webView.getAppId() : "",
                webView.getCurrentPath() != null ? webView.getCurrentPath() : ""
            );
        }
    }

    @Override
    public void onPageFinished(WebView view, String url) {
        super.onPageFinished(view, url);
        Log.d(TAG, "Page finished loading: " + url);

        // Inject JavaScript to simulate real browser environment for feishu pages
        if (url.contains("feishu.cn")) {
            String jsCode =
                "javascript:(function(){" +
                // Disable WebAuthn
                "if (typeof window.PublicKeyCredential !== 'undefined') {" +
                "  delete window.PublicKeyCredential;" +
                "}" +
                "if (typeof navigator.credentials !== 'undefined') {" +
                "  navigator.credentials = undefined;" +
                "}" +
                "window.webAuthnSupported = false;" +
                // Hide WebView indicators
                "delete window.AndroidInterface;" +
                "delete window.webkit;" +
                "delete window._cordovaNative;" +
                // Override navigator properties to look like Chrome
                "Object.defineProperty(navigator, 'userAgent', {" +
                "  value: 'Mozilla/5.0 (Linux; Android " + android.os.Build.VERSION.RELEASE + "; " + android.os.Build.MODEL + ") AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.6099.210 Mobile Safari/537.36'," +
                "  writable: false" +
                "});" +
                "Object.defineProperty(navigator, 'vendor', {" +
                "  value: 'Google Inc.'," +
                "  writable: false" +
                "});" +
                // Add Chrome-specific properties
                "window.chrome = window.chrome || {};" +
                "window.chrome.runtime = window.chrome.runtime || {};" +
                "console.log('Browser environment simulated by LingXia');" +
                "})()";
            view.evaluateJavascript(jsCode, null);
            Log.d(TAG, "Injected browser simulation script for: " + url);
        }

        LingXiaWebView webView = webViewRef.get();
        if (webView != null) {
            webView.setPageLoaded(true);
            webView.resetViewport();
            webView.onPageFinished(
                webView.getAppId() != null ? webView.getAppId() : "",
                webView.getCurrentPath() != null ? webView.getCurrentPath() : ""
            );
        }
    }

    @Override
    public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
        if (request != null && request.getUrl() != null) {
            String url = request.getUrl().toString();
            Log.d(TAG, "Should override URL loading: " + url);

            // Extract scheme from URL
            String scheme = "";
            int schemeEnd = url.indexOf("://");
            if (schemeEnd > 0) {
                scheme = url.substring(0, schemeEnd);
            } else {
                return false; // Invalid URL, don't override
            }

            // Handle lingxia scheme or block non-https schemes
            switch (scheme) {
                case "lx":
                    return true; // Always intercept lingxia scheme
                case "https":
                    return false; // Allow https URLs
                case "lark":
                    return false;
                default:
                    return true; // Block all other schemes
            }
        }
        return false;
    }

    @Override
    public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
        super.onReceivedError(view, request, error);
        Log.e(TAG, "Error loading page: " + error.getDescription() +
              ", code: " + error.getErrorCode() +
              ", failing URL: " + (request != null ? request.getUrl() : "unknown"));
    }

    @Override
    public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
        String url = request.getUrl().toString();
        String method = request.getMethod();

        // Log all requests for debugging
        Log.d(TAG, "Intercepting request: " + method + " " + url);

        // Test code: Skip interception for lark scheme
         if (url.startsWith("lark://")) {
             Log.d(TAG, "Handling lark scheme request: " + url);
             return handleLarkScheme(url, request);
         }

        // Handle known CORS problematic domains with direct response
        if (isCorsProblematicDomain(url)) {
            Log.d(TAG, "CORS problematic domain detected, intercepting: " + url);
            return handleCorsRequest(url, request);
        }

        // Convert headers to JSON string
        JSONObject headersJson = new JSONObject();
        try {
            for (String key : request.getRequestHeaders().keySet()) {
                headersJson.put(key, request.getRequestHeaders().get(key));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error converting headers to JSON", e);
        }

        LingXiaWebView webView = webViewRef.get();
        if (webView != null) {
            // Call native to handle request
            LingXiaWebView.WebResourceResponseData response = webView.handleRequest(
                webView.getAppId() != null ? webView.getAppId() : "",
                url,
                method,
                headersJson.toString()
            );

            if (response == null) {
                return null;
            }

            return new WebResourceResponse(
                response.mimeType,
                response.encoding,
                response.statusCode,
                response.reasonPhrase,
                response.responseHeaders,
                response.data != null ? new ByteArrayInputStream(response.data) : null
            );
        }

        return null;
    }

    private boolean isCorsProblematicDomain(String url) {
        return url.contains("slardar-bd.feishu.cn") ||
               url.contains("mcs-bd.feishu.cn") ||
               url.contains("vcs.snssdk.com") ||
               url.contains("verify.snssdk.com") ||
               url.contains("mcs.zijieapi.com") ||
               url.contains("mcs.snssdk.com") ||
               url.contains("accounts.feishu.cn/accounts/config") ||  // Broader feishu config matching
               url.contains("accounts.feishu.cn/accounts/page");     // Also handle page requests
    }

    private WebResourceResponse handleCorsRequest(String url, WebResourceRequest request) {
        Log.d(TAG, "Handling CORS request directly: " + url);

        // Create appropriate response based on URL
        String responseData;
        String contentType = "application/json";

        if (url.contains("accounts/config/gray")) {
            // Return a comprehensive gray config response for feishu
            responseData = "{\"code\":0,\"msg\":\"success\",\"data\":{" +
                "\"enable_webauthn\":false," +
                "\"enable_password_login\":true," +
                "\"enable_sms_login\":true," +
                "\"enable_qr_login\":true," +
                "\"webauthn_supported\":false" +
                "}}";
        } else if (url.contains("accounts/config")) {
            // General config response
            responseData = "{\"code\":0,\"msg\":\"success\",\"data\":{\"status\":\"ok\"}}";
        } else if (url.contains("accounts/page")) {
            // Page request - return HTML content type
            contentType = "text/html";
            responseData = "<!DOCTYPE html><html><head><title>Loading...</title></head><body><script>window.location.reload();</script></body></html>";
        } else {
            // Default empty response
            responseData = "{\"code\":0,\"data\":{}}";
        }
        ByteArrayInputStream inputStream = new ByteArrayInputStream(responseData.getBytes());

        // Create response headers - use the correct origin
        java.util.Map<String, String> headers = new java.util.HashMap<>();

        // Always use accounts.feishu.cn as the origin since that's where the requests come from
        String origin = "https://accounts.feishu.cn";

        headers.put("Access-Control-Allow-Origin", origin);
        headers.put("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        headers.put("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, x-setting-flag, x-mcs-appkey, x-tt-logid");
        headers.put("Access-Control-Allow-Credentials", "true");
        headers.put("Content-Type", contentType);

        return new WebResourceResponse(
            contentType,
            "utf-8",
            200,
            "OK",
            headers,
            inputStream
        );
    }

    private WebResourceResponse handleLarkScheme(String url, WebResourceRequest request) {
        Log.d(TAG, "Processing lark scheme URL: " + url);

        try {
            // Fix the lark URL by adding missing parameters
            String fixedUrl = fixLarkUrl(url);
            Log.d(TAG, "Fixed lark URL: " + fixedUrl);

            // Try to launch the lark app via Intent
            android.content.Context context = webViewRef.get().getContext();
            if (context != null) {
                android.content.Intent intent = new android.content.Intent(android.content.Intent.ACTION_VIEW);
                intent.setData(android.net.Uri.parse(fixedUrl));
                intent.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK);

                // Try to start the activity
                context.startActivity(intent);
                Log.d(TAG, "Successfully launched lark app with URL: " + fixedUrl);

                // Return a simple success page
                String htmlContent = createSuccessPage();
                ByteArrayInputStream inputStream = new ByteArrayInputStream(htmlContent.getBytes());

                return new WebResourceResponse(
                    "text/html",
                    "utf-8",
                    200,
                    "OK",
                    new java.util.HashMap<String, String>(),
                    inputStream
                );
            }
        } catch (Exception e) {
            Log.w(TAG, "Failed to launch lark app: " + e.getMessage());
        }

        // Fallback: Return error page
        String htmlContent = createErrorPage();
        ByteArrayInputStream inputStream = new ByteArrayInputStream(htmlContent.getBytes());

        return new WebResourceResponse(
            "text/html",
            "utf-8",
            200,
            "OK",
            new java.util.HashMap<String, String>(),
            inputStream
        );
    }

    private String fixLarkUrl(String originalUrl) {
        try {
            android.net.Uri uri = android.net.Uri.parse(originalUrl);
            android.net.Uri.Builder builder = uri.buildUpon();

            // Get current context to determine package name
            android.content.Context context = webViewRef.get().getContext();
            String packageName = context != null ? context.getPackageName() : "com.lingxia.example.lxapp";

            // Fix missing parameters
            if (uri.getQueryParameter("bundle_id") == null || uri.getQueryParameter("bundle_id").isEmpty()) {
                builder.appendQueryParameter("bundle_id", packageName);
                Log.d(TAG, "Added bundle_id: " + packageName);
            }

            if (uri.getQueryParameter("schema") == null || uri.getQueryParameter("schema").isEmpty()) {
                builder.appendQueryParameter("schema", "https");
                Log.d(TAG, "Added schema: https");
            }

            // For qr_code, we can't easily fix it, but we can add a placeholder
            if (uri.getQueryParameter("qr_code") == null || uri.getQueryParameter("qr_code").isEmpty()) {
                // Generate a simple placeholder - in real implementation, this should come from the auth flow
                String placeholder = "lingxia_auth_" + System.currentTimeMillis();
                builder.appendQueryParameter("qr_code", placeholder);
                Log.d(TAG, "Added qr_code placeholder: " + placeholder);
            }

            return builder.build().toString();
        } catch (Exception e) {
            Log.e(TAG, "Error fixing lark URL: " + e.getMessage(), e);
            return originalUrl;
        }
    }

    private String createSuccessPage() {
        return "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>Opening Lark</title>" +
               "<style>body{font-family:system-ui,sans-serif;text-align:center;padding:50px;background:#f5f5f5;}" +
               ".container{background:white;border-radius:8px;padding:40px;max-width:400px;margin:0 auto;box-shadow:0 2px 10px rgba(0,0,0,0.1);}" +
               ".success{color:#27ae60;font-size:48px;margin-bottom:20px;}</style></head>" +
               "<body><div class='container'><div class='success'>✓</div><h2>Opening Lark App...</h2>" +
               "<p>Please complete the authorization in Lark app.</p>" +
               "<p><small>You may need to return to this app manually after authorization.</small></p></div></body></html>";
    }

    private String createErrorPage() {
        return "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>Error</title></head>" +
               "<body><h2>Error</h2><p>Failed to open Lark app. Please make sure Lark is installed.</p></body></html>";
    }
}
