server API return app list and type(H5 url, or lxapp), name, icon
New API 平台： 针对业务场景定义 prompt， 支持版本或 preview， 客户可以定制或优化

like setData, adding postMessage to support post message from native js to view/webview
postmessage(message, origin) origin is optional
e.x. push LLM reply to view
web-bridge.js: print detail message if it's debug on, turn on when injecting base on debug mode

lingxia-user:  preserved for user and local

lingxia-mcp: server w/ sse. sse can run javascript to control lxapp
     SSE client -- SSE server on PC/MPC server --- MPC client
     integrate into cli
     lxapp must be in debug mode
     GUI: devtool, log view

lingxia-cli: tempalte, new, build, standalone

tabbar: click -> switch or send event
        remove all about top. only support left/right, bottom ?


lingxia builer:
  better name -> publish
  support bun etc. currently, hardcode 'npm'

openlxapp: with extra flags + debug
remove set devtools



?????????: harmony native 发起创建推送 tsfn， 延迟太大(one solution: precreate run in async, and wait creating done by tsfn callback)
create webview:
homelxapp on init evnet, start creating init route webview, arkts after init returen polling whether
it's done, if not settimeout to let engine has chance to poll tsfn queue to perform creating
on lxapp opened : stop creating init webview, create first for open lxapp at rust
lxapp introduce dispatch(signel thread tokio or pure thread)
引入 dispatch 后， kotlin 等需要移除 Thread/task，因为在 rust 端统一处理了
// 延迟 ms 毫秒后兑现
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}
// 使用示例
async function demo() {
  console.log('start');
  await delay(1000);          // 非阻塞等待 1 秒
  console.log('1s later');
}



about user agent
https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/web-default-useragent



devtools with MCP
1. capture log. realtime  with filter susbsytem/category
2. tools for view: click etc. opendia
3. capture, switch Page(tab)
4. hotreload -- upgrade lxapp packages and restart
5. how to capture request/response for logic fetch

integrate to cloud:
1. download and update
2. login(user, device info)
3. remote log trace(appid, path). collect and zip, upload to cloud
4. cloud function like wechat. lx.cloud.callFunc("name", {})

mobile api
finger print(unique device id)
    figure/device id:
      bind user. after done, next time, only input mobile phone to verify it's in org, device id same, auto login
      bind store with public IP & location
location(integrated)
camera, photo album, qr(harmony: arkts vs native)
file reader: pdf, excel, word, png, native API
deeplink(open lingxia app in dingtak etc, integrated)
push message(huawei, ios, xiaomi, oppo)

App event: on_backgorupd, on_capture

LX JS API:
navigator, tabbar, location ...
体验版,开发版本 基础版本号
reopen lxapp: close(destroy?) and start
OnScrollChangeListener(lower)

Rong:
  Harmony JVM(code done, but not test)
  SSE
  ReadableStream
  V8

extra(lower priority):
printer support
scanner



tauri  icon:
https://github.com/tauri-apps/tauri/blob/cf0b3588a312d9c25ea49e82d40675ea94fcbfdd/crates/tauri-codegen/src/context.rs#L465
https://github.com/tauri-apps/tauri/blob/cf0b3588a312d9c25ea49e82d40675ea94fcbfdd/crates/tauri-utils/src/config.rs#L1225

lx:// Get
https: Get or others. -> is allowed domain ->check lx:// https://  yes, continue to go
                      -> is should proxed -> yes. build Request -> let lxapp to proxy

                          not allowed: reject directly
                          allow: proxyed, or direct to response

                          continue to process
                          take over then proces

                          ios: does not support intercept resoruce request in https ?
                          anroid: only supprot get method ?

 adb shell run-as com.lingxia.example.lxapp cp -r /data/user/0/com.lingxia.example.lxapp/files/lingxia/lxapps/homelxapp/. /data/user/0/com.lingxia.example.lxapp/files/lingxia/lxapps/95dc2dcfcccc191/
